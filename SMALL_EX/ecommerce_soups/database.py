import sqlite3
import bcrypt
import pandas as pd
from datetime import datetime
import os

class Database:
    def __init__(self, db_path="soup_ecommerce.db"):
        self.db_path = db_path
        self.init_database()
        
    def get_connection(self):
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialize database with required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Products table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                price REAL NOT NULL,
                category TEXT NOT NULL,
                type TEXT NOT NULL CHECK (type IN ('veg', 'non-veg')),
                stock_quantity INTEGER DEFAULT 0,
                image_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Cart table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cart (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 1,
                added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # Orders table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                total_amount REAL NOT NULL,
                status TEXT DEFAULT 'pending',
                shipping_address TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Order items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL NOT NULL,
                FOREIGN KEY (order_id) REFERENCES orders (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Add sample data if tables are empty
        self.add_sample_data()
    
    def add_sample_data(self):
        """Add sample soup products"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if products already exist
        cursor.execute("SELECT COUNT(*) FROM products")
        if cursor.fetchone()[0] > 0:
            conn.close()
            return
        
        sample_products = [
            ("Tomato Basil Soup", "Rich and creamy tomato soup with fresh basil", 45.00, "Classic", "veg", 50, "https://via.placeholder.com/300x200?text=Tomato+Basil"),
            ("Chicken Noodle Soup", "Traditional chicken soup with tender noodles", 65.00, "Classic", "non-veg", 30, "https://via.placeholder.com/300x200?text=Chicken+Noodle"),
            ("Mushroom Cream Soup", "Creamy mushroom soup with herbs", 55.00, "Gourmet", "veg", 25, "https://via.placeholder.com/300x200?text=Mushroom+Cream"),
            ("Beef Barley Soup", "Hearty beef soup with barley and vegetables", 75.00, "Hearty", "non-veg", 20, "https://via.placeholder.com/300x200?text=Beef+Barley"),
            ("Vegetable Minestrone", "Italian vegetable soup with pasta", 50.00, "Classic", "veg", 40, "https://via.placeholder.com/300x200?text=Minestrone"),
            ("Fish Chowder", "Creamy fish soup with potatoes", 80.00, "Gourmet", "non-veg", 15, "https://via.placeholder.com/300x200?text=Fish+Chowder"),
            ("Lentil Soup", "Protein-rich lentil soup with spices", 40.00, "Healthy", "veg", 60, "https://via.placeholder.com/300x200?text=Lentil"),
            ("Mutton Soup", "Spicy mutton soup with traditional spices", 90.00, "Spicy", "non-veg", 12, "https://via.placeholder.com/300x200?text=Mutton"),
            ("Spinach Corn Soup", "Healthy spinach and corn soup", 35.00, "Healthy", "veg", 45, "https://via.placeholder.com/300x200?text=Spinach+Corn"),
            ("Prawn Bisque", "Luxurious prawn soup with cream", 120.00, "Gourmet", "non-veg", 8, "https://via.placeholder.com/300x200?text=Prawn+Bisque")
        ]
        
        cursor.executemany('''
            INSERT INTO products (name, description, price, category, type, stock_quantity, image_url)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', sample_products)
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        """Hash password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password, hashed):
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def create_user(self, username, email, password, full_name, phone=None, address=None):
        """Create a new user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            password_hash = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, full_name, phone, address)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, email, password_hash, full_name, phone, address))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return user_id
        except sqlite3.IntegrityError as e:
            conn.close()
            if "username" in str(e):
                raise ValueError("Username already exists")
            elif "email" in str(e):
                raise ValueError("Email already exists")
            else:
                raise ValueError("User creation failed")
    
    def authenticate_user(self, login, password):
        """Authenticate user with username/email and password"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if login is email or username
        cursor.execute('''
            SELECT id, username, email, password_hash, full_name 
            FROM users 
            WHERE username = ? OR email = ?
        ''', (login, login))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and self.verify_password(password, user[3]):
            return {
                'id': user[0],
                'username': user[1],
                'email': user[2],
                'full_name': user[4]
            }
        return None
    
    def get_products(self, category=None, product_type=None):
        """Get products with optional filtering"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM products WHERE 1=1"
        params = []
        
        if category:
            query += " AND category = ?"
            params.append(category)
        
        if product_type:
            query += " AND type = ?"
            params.append(product_type)
        
        query += " ORDER BY name"
        
        cursor.execute(query, params)
        products = cursor.fetchall()
        conn.close()
        
        return products
    
    def get_product_by_id(self, product_id):
        """Get a single product by ID"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
        product = cursor.fetchone()
        conn.close()
        
        return product
    
    def add_to_cart(self, user_id, product_id, quantity=1):
        """Add item to cart"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if item already in cart
        cursor.execute('''
            SELECT id, quantity FROM cart 
            WHERE user_id = ? AND product_id = ?
        ''', (user_id, product_id))
        
        existing = cursor.fetchone()
        
        if existing:
            # Update quantity
            new_quantity = existing[1] + quantity
            cursor.execute('''
                UPDATE cart SET quantity = ? 
                WHERE id = ?
            ''', (new_quantity, existing[0]))
        else:
            # Add new item
            cursor.execute('''
                INSERT INTO cart (user_id, product_id, quantity)
                VALUES (?, ?, ?)
            ''', (user_id, product_id, quantity))
        
        conn.commit()
        conn.close()
    
    def get_cart_items(self, user_id):
        """Get cart items for user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT c.id, c.quantity, p.id, p.name, p.price, p.image_url, p.type
            FROM cart c
            JOIN products p ON c.product_id = p.id
            WHERE c.user_id = ?
            ORDER BY c.added_at DESC
        ''', (user_id,))
        
        items = cursor.fetchall()
        conn.close()
        
        return items
    
    def remove_from_cart(self, cart_id):
        """Remove item from cart"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM cart WHERE id = ?", (cart_id,))
        conn.commit()
        conn.close()
    
    def update_cart_quantity(self, cart_id, quantity):
        """Update cart item quantity"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if quantity <= 0:
            cursor.execute("DELETE FROM cart WHERE id = ?", (cart_id,))
        else:
            cursor.execute('''
                UPDATE cart SET quantity = ? WHERE id = ?
            ''', (quantity, cart_id))
        
        conn.commit()
        conn.close()
    
    def create_order(self, user_id, cart_items, shipping_address):
        """Create order from cart items"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Calculate total
            total_amount = sum(item[2] * item[1] for item in cart_items)  # price * quantity
            
            # Create order
            cursor.execute('''
                INSERT INTO orders (user_id, total_amount, shipping_address)
                VALUES (?, ?, ?)
            ''', (user_id, total_amount, shipping_address))
            
            order_id = cursor.lastrowid
            
            # Add order items
            for item in cart_items:
                cursor.execute('''
                    INSERT INTO order_items (order_id, product_id, quantity, price)
                    VALUES (?, ?, ?, ?)
                ''', (order_id, item[0], item[1], item[2]))
            
            # Clear cart
            cursor.execute("DELETE FROM cart WHERE user_id = ?", (user_id,))
            
            conn.commit()
            conn.close()
            
            return order_id
        except Exception as e:
            conn.rollback()
            conn.close()
            raise e
    
    def get_user_orders(self, user_id):
        """Get orders for user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, total_amount, status, shipping_address, created_at
            FROM orders
            WHERE user_id = ?
            ORDER BY created_at DESC
        ''', (user_id,))
        
        orders = cursor.fetchall()
        conn.close()
        
        return orders
